<?php
// Disable any output buffering and error display
ob_start();
ini_set('display_errors', 0);
error_reporting(0);

// Set content type to JSON
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Database path
    $db_path = __DIR__ . '/../../dbjcs2112ew.sqlite';

    if (!file_exists($db_path)) {
        throw new Exception('Database file not found');
    }

    // Create PDO connection
    $pdo = new PDO('sqlite:' . $db_path);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get pagination parameters
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? min(1000, max(10, (int)$_GET['limit'])) : 100; // Default 100, max 1000 for Pinterest CSV
    $offset = ($page - 1) * $limit;

    // Special handling for Pinterest CSV generation - get ALL products
    $is_pinterest_csv = isset($_GET['pinterest_csv']) && $_GET['pinterest_csv'] === '1';
    if ($is_pinterest_csv) {
        $limit = 10000; // Set very high limit to get all products
        $offset = 0;
        $page = 1;
    }

    // Get total count for pagination
    $count_sql = "SELECT COUNT(*) as total FROM products p WHERE p.is_active = 1";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute();
    $total_products = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Get ALL products with their images, SEO data, and categories (including all product types)
    $sql = "SELECT
                p.id,
                p.name_pt as name,
                p.slug,
                p.description_pt as description,
                p.base_price as price,
                p.product_type,
                p.seo_title,
                p.seo_description,
                p.seo_keywords,
                (SELECT pi2.filename FROM product_images pi2
                 WHERE pi2.product_id = p.id AND pi2.is_default = 1
                 LIMIT 1) as image_filename,
                (SELECT c2.name FROM categories c2
                 JOIN product_categories pc2 ON c2.id = pc2.category_id
                 WHERE pc2.product_id = p.id AND c2.is_active = 1
                 ORDER BY c2.name ASC LIMIT 1) as primary_category
            FROM products p
            WHERE p.is_active = 1
            ORDER BY p.id ASC
            LIMIT :limit OFFSET :offset";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Process products to ensure proper data types and handle nulls
    $processed_products = [];
    foreach ($products as $product) {
        $processed_products[] = [
            'id' => (int)$product['id'],
            'name' => $product['name'] ?? 'Unnamed Product',
            'slug' => $product['slug'] ?? '',
            'description' => $product['description'] ?? '',
            'price' => (float)($product['price'] ?? 0),
            'product_type' => $product['product_type'] ?? 'regular',
            'seo_title' => $product['seo_title'] ?? '',
            'seo_description' => $product['seo_description'] ?? '',
            'seo_keywords' => $product['seo_keywords'] ?? '',
            'image_filename' => $product['image_filename'] ?? null,
            'primary_category' => $product['primary_category'] ?? null
        ];
    }

    // Clear any output buffer
    ob_clean();

    // Calculate pagination info
    $total_pages = ceil($total_products / $limit);
    $has_next = $page < $total_pages;
    $has_prev = $page > 1;

    // Return success response
    echo json_encode([
        'success' => true,
        'products' => $processed_products,
        'count' => count($processed_products),
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_products' => $total_products,
            'limit' => $limit,
            'has_next' => $has_next,
            'has_prev' => $has_prev
        ]
    ]);

} catch (Exception $e) {
    // Clear any output buffer
    ob_clean();

    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'products' => []
    ]);
}
?>
