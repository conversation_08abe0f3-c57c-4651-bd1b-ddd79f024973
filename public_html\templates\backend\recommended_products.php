<?php
// Recommended Products Management Template
?>

<h1><PERSON><PERSON>r Produtos Recomendados</h1>



<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Adicionar Produto Recomendado</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($available_products)): ?>
                    <form id="add-recommended-form" method="post" action="admin.php?section=products&action=recommended&<?= get_session_id_param() ?>">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?? '' ?>">
                        <input type="hidden" name="add_recommended" value="1">
                        
                        <div class="mb-3">
                            <label for="product_id" class="form-label">Selecionar Produto:</label>
                            <select name="product_id" id="product_id" class="form-select" required>
                                <option value="">-- Selecione um produto --</option>
                                <?php foreach ($available_products as $product): ?>
                                    <option value="<?= $product['id'] ?>">
                                        <?= sanitize_input($product['name']) ?> 
                                        (<?= ucfirst($product['product_type']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-plus-circle"></i> Adicionar aos Recomendados
                        </button>
                    </form>
                <?php else: ?>
                    <p class="text-muted">Todos os produtos ativos já estão na lista de recomendados ou não há produtos disponíveis.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Produtos Recomendados Atuais</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($current_recommended)): ?>
                    <div class="list-group">
                        <?php foreach ($current_recommended as $product): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?= sanitize_input($product['name']) ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        Tipo: <?= ucfirst($product['product_type']) ?> | 
                                        Slug: <?= sanitize_input($product['slug']) ?>
                                    </small>
                                </div>
                                <form method="post" action="admin.php?section=products&action=recommended&<?= get_session_id_param() ?>" style="display: inline;">
                                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?? '' ?>">
                                    <input type="hidden" name="remove_recommended" value="1">
                                    <input type="hidden" name="product_id" value="<?= $product['id'] ?>">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                            onclick="return confirm('Tem certeza que deseja remover este produto dos recomendados?');"
                                            title="Remover dos Recomendados">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i> 
                            Os produtos recomendados aparecerão em ordem aleatória na página inicial.
                        </small>
                    </div>
                <?php else: ?>
                    <p class="text-muted">Nenhum produto recomendado configurado ainda.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="mt-4">
    <a href="admin.php?section=products&<?= get_session_id_param() ?>" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> Voltar à Lista de Produtos
    </a>
</div>

<script>
// Auto-submit form when product is selected (optional enhancement)
document.getElementById('product_id')?.addEventListener('change', function() {
    if (this.value) {
        // Optional: Auto-submit when product is selected
        // document.getElementById('add-recommended-form').submit();
    }
});
</script>
